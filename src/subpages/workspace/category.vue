<script setup lang="ts">
import { useCategoryStore } from '@/stores/category'

const categoryStore = useCategoryStore()

// 分页相关数据
const pageNum = ref(1)
const pageSize = ref(15)
const count = ref(0)
const isLoadingMore = ref(false)
const hasNoMoreData = ref(false)
const alertCategoryTitle = ref('创建分类')
const title = ref('')
const description = ref('')
const SortAsc = ref(0)
const alertCategory = ref(null)
const deleteConfirmPopup = ref(null)
const categoryList = ref()
const categoryItems = ref([])

async function getCategoryList() {
  try {
    const results = await categoryStore.getAppCategories(pageNum.value, pageSize.value)

    if (results) {
      categoryList.value = results
      if (pageNum.value === 1) {
        categoryItems.value = results.data
      }
      else {
        categoryItems.value = [...categoryItems.value, ...results.data]
      }
      count.value = results.count
    }
  }
  catch (error) {
    console.error('获取记忆列表出错:', error)
  }
  finally {
    isLoadingMore.value = false
  }
}

function addCategory() {
  alertCategoryTitle.value = '创建分类'
  resetCagegoryData()
  alertCategory.value?.open()
}

function resetCagegoryData() {
  updateId.value = ''
  title.value = ''
  description.value = ''
}

const updateId = ref('')
function editCategory(item) {
  alertCategoryTitle.value = '编辑记忆'
  updateId.value = item.id || ''
  title.value = item.title || ''
  description.value = item.description || ''
  alertCategory.value?.open()
}

function categoryClose() {
  alertCategory.value?.close()
  resetCagegoryData()
}

async function categoryPost() {
  try {
    if (alertCategoryTitle.value === '创建分类') {
      await categoryStore.createAppCategory(title.value, description.value, SortAsc.value)
    }
    else {
      await categoryStore.updateAppCategory(updateId.value, title.value, description.value, SortAsc.value)
    }
  }
  catch (err) {
    console.error('记忆操作出错:', err)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  categoryClose()
  resetCagegoryData()
  await getCategoryList()
}

// 存储待删除的分类 ID
const deleteCategoryId = ref('')
// 显示删除确认弹窗
function showDeleteConfirm(id: string) {
  deleteCategoryId.value = id
  deleteConfirmPopup.value?.open()
}
// 关闭删除确认弹窗
function closeDeleteConfirm() {
  deleteConfirmPopup.value?.close()
}
// 确认删除分类
async function confirmDelete() {
  try {
    await categoryStore.deleteAppCategory(deleteCategoryId.value)
    await getCategoryList()
    uni.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 2000,
    })
  }
  catch (error) {
    console.error('删除分类出错:', error)
    uni.showToast({
      title: '删除失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  closeDeleteConfirm()
}

onReachBottom(async () => {
  if (isLoadingMore.value || hasNoMoreData.value) {
    return
  }
  if (pageNum.value * pageSize.value >= count.value) {
    hasNoMoreData.value = true
    return
  }
  isLoadingMore.value = true
  pageNum.value++
  await getCategoryList()
})

onMounted(async () => {
  await getCategoryList()
})
</script>

<template>
  <!-- Memory List -->
  <view class="shadow-xs mt-2 rounded-lg bg-white p-3">
    <view class="mb-4 flex items-center justify-between border-b border-gray-300 pb-2">
      <view class="text-lg font-medium">
        分类列表
      </view>
      <view class="flex items-center text-blue-500" @click="addCategory">
        <uni-icons type="plusempty" size="16" color="#3B82F6" class="ml-2" />
        <view>新增分类</view>
      </view>
    </view>

    <!-- Memory Items -->
    <view class="space-y-4">
      <view v-for="(item, index) in categoryItems" :key="index" class="w-full border-b border-gray-100 pb-2">
        <view class="flex flex-row items-center justify-between">
          <view class="max-w-64">
            <view class="truncate font-medium text-black">
              名称：{{ item.title }}
            </view>
            <view class="mt-1  truncate text-sm text-gray-500">
              描述：{{ item.description }}
            </view>
          </view>
          <view class="flex flex-row items-baseline justify-end">
            <!-- 编辑图标 -->
            <uni-icons type="more-filled" size="15" class="m-auto mr-4" color="#9CA3AF" @click="editCategory(item)" />
            <!-- 删除图标 -->
            <uni-icons type="trash" size="15" class="m-auto" color="#9CA3AF" @click="showDeleteConfirm(item.id)" />
          </view>
        </view>
      </view>
    </view>
    <!-- 加载更多提示 -->
    <view v-if="isLoadingMore" class="py-2 text-center text-gray-500">
      <uni-icons type="loading" size="20" /> 加载中...
    </view>
    <!-- 没有更多数据提示 -->
    <view v-if="hasNoMoreData" class="py-2 text-center text-gray-500">
      没有更多数据了
    </view>
  </view>

  <!-- 提示窗示例 -->
  <uni-popup ref="alertCategory" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="input" class="!w-96" :title="alertCategoryTitle" :before-close="true"
      @close="categoryClose" @confirm="categoryPost"
    >
      <view class="flex flex-col items-start space-y-4 p-6">
        <view class="w-full">
          <label class="mb-2 block text-sm font-medium text-gray-700">分类名称</label>
          <input
            v-model="title"
            class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            placeholder="请输入分类名称"
          >
        </view>
        <view class="w-full">
          <label class="mb-2 block text-sm font-medium text-gray-700">分类描述</label>
          <textarea
            v-model="description"
            class="min-h-[120px] w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            placeholder="请输入分类描述"
          />
        </view>
      </view>
    </uni-popup-dialog>
  </uni-popup>

  <!-- 删除确认弹窗 -->
  <uni-popup ref="deleteConfirmPopup" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="default" class="!w-96" title="确认删除" :before-close="true"
      @close="closeDeleteConfirm" @confirm="confirmDelete"
    >
      <view class="p-6 text-center">
        是否删除此分类？
      </view>
    </uni-popup-dialog>
  </uni-popup>
</template>
