
/**
* 将 LaTeX 公式转换为 KaTeX 兼容的语法
* @param {string} latex 原始 LaTeX 公式
* @returns {string} 转换后的 KaTeX 代码
*/
export function convertLatexToKaTeX(latex) {
	  let converted = latex;
	
	  // 替换 LaTeX 环境为 KaTeX 分隔符
	  converted = converted.replace(
		/\\begin{(equation|align|gather|pmatrix)\*?}([\s\S]*?)\\end{\1\*?}/g,
		(match, env, content) => {
		  // 将 align 环境转换为 aligned
		  if (env === 'align') return `\\begin{aligned}${content}\\end{aligned}`;
		else if (env === "pmatrix")
				return `\\begin{bmatrix}${content}\\end{bmatrix}`;
		  return `${content}`;
		}
	  );
	
	  //替换不支持的符号或命令
	  const symbolReplacements = {
		'\\Rightarrow': '\\implies',
		'\\underset{': '\\underbrace{',
		'\\overset{': '\\overbrace{',
	  };
	  Object.entries(symbolReplacements).forEach(([from, to]) => {
		converted = converted.replace(new RegExp(from, 'g'), to);
	  });
	
	  // 处理自定义命令（示例：将 \vect{v} 转换为 \mathbf{v}）
	  converted = converted.replace(/\\vect{([^}]+)}/g, '\\mathbf{$1}');
	
	  // 移除 LaTeX 注释
	  // converted = converted.replace(/%.*$/gm, '');
	
	  return converted;
	}