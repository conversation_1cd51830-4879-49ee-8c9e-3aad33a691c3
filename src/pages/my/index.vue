<script setup lang="ts">
import PhoneBindPopup from '@/components/common/PhoneBindPopup.vue'
import Support from '@/components/common/Support.vue'
import WechatBindPopup from '@/components/common/WechatBindPopup.vue'
import MenuSection from '@/components/my/menuSection.vue'

import MenuiIemList from '@/components/my/my.json'
import { useAgentStore } from '@/stores/agent'
import { useAuthStore } from '@/stores/auth'

import { useUsersStore } from '@/stores/user'
import { ref } from 'vue'

const version = import.meta.env.VITE_APP_VERSION
const agentStore = useAgentStore()
const store = useAuthStore()
const { user, userRoleInfo, loginStatus } = storeToRefs(store)
const { checkSession, logout } = store
const accountRole = userRoleInfo // 角色类型
const spaceOptions = ref() // 初始化为空数组
const spacePopup = ref() // 用于控制uni-popup的ref
const userStore = useUsersStore()
const showPhoneBindPopup = ref(false)
const showWechatBindPopup = ref(false)
const checkType = ref('join')
// 获取空间数据
async function getAvailableCompanys() {
  const result = await userStore.getUserCompanyList()
  spaceOptions.value = result.companyList
}
onShow(async () => {
  try {
    // 获取当前用户可以切换的空间

    // 获取用户信息
    // await checkSession()
    // fetchUser()
  }
  catch (error) {
    console.error('数据获取出错:', error)
  }
})

// 商家中心菜单项
async function changeWorkFlow() {
  if (accountRole?.value?.current_company_id == null) {
    uni.showToast({
      title: '暂未加入空间',
      icon: 'error',
    })
    return
  }
  await getAvailableCompanys()
  spacePopup.value?.open() // 打开uni-popup
}

function navigateToBusinessInfo() {
  if (loginStatus.value === false) {
    uni.navigateTo({
      url: '/pages/auth/login',
    })
  }
  else {
    uni.navigateTo({
      url: '/pages/my/info',
    })
  }
}

// 关闭uni-popup的方法
function closeSpacePopup() {
  spacePopup.value?.close()
}

// 处理用户切换当前空间的方法 TODO::需要重新加载全部界面
async function handleSpaceSelection(option: any) {
  try {
    await agentStore.switchUserCurrentCompany(option.company_id)
    closeSpacePopup()
    await checkSession()
    // 使用 uni.navigateBack 返回到上一页
    // 延迟一段时间后使用 uni.redirectTo 重新加载当前页面
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/my/index', // 请将 yourPageName 替换为实际的页面路径
      })
    }, 100)
  }
  catch (error) {
    console.error('切换空间时出错:', error)
    uni.showToast({
      title: '空间切换失败',
      icon: 'error',
    })
  }
}

onShareAppMessage((_res) => {
  return {
    title: 'AI智能员工 一人可抵百人用', // 标题
    path: '/pages/index/index', // 分享路径
    imageUrl: `${import.meta.env.VITE_SHAREIMG}`, // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  return {
    title: 'AI智能员工 一人可抵百人用', // 标题
    path: '/pages/index/index', // 分享路径
    imageUrl: `${import.meta.env.VITE_SHAREIMG}`, // 分享图
  }
})

// 处理手机号绑定成功
function handlePhoneBindSuccess(type: string) {
  setTimeout(() => {
    if (type === 'join') {
      uni.reLaunch({
        url: '/pages/workspace/join',
      })
    }
    else if (type === 'create') {
      uni.reLaunch({
        url: '/subpages/workspace/create',
      })
    }
  }, 1000)
}

// 显示手机号绑定弹窗
function bindPhonePoup() {
  showPhoneBindPopup.value = true
}

// 显示微信绑定弹窗
function bindWechatPopup() {
  showWechatBindPopup.value = true
}

// 处理微信绑定成功
function handleWechatBindSuccess() {
  uni.showToast({
    title: '微信账号绑定成功',
    icon: 'success',
    duration: 1000,
  })
}

function changeCheckType(type) {
  checkType.value = type
}
</script>

<template>
  <view class="profile-page bg-gray-50 pb-2 ">
    <view class="user-header flex items-center justify-between bg-white px-4 py-3">
      <view class="user-info flex items-center" @click="navigateToBusinessInfo">
        <image :src="user?.user_metadata.avatar_url || '/static/images/author.png'" class="user-avatar" />
        <view class="user-details flex flex-col">
          <view class="user-name ">
            {{ loginStatus ? user?.user_metadata?.full_name || "微信用户" : '点击登录' }}
          </view>
        </view>
      </view>
      <view class="flex items-center">
        <view class="space-switcher" @click="changeWorkFlow">
          <view class="space-text truncate text-sm font-medium">
            {{ accountRole?.current_company_id ? accountRole?.current_company_name : '暂未加入空间' }}
          </view>
          <uni-icons fontFamily="CustomFont" size="20" color="#ffffff">
            {{ '\ue6a7' }}
          </uni-icons>
        </view>
      </view>
    </view>

    <!-- <MenuSection v-if="accountRole.current_company_role_name === 'user'" title="充电力值" :items="MenuiIemList.client.recharge" -->
    <!-- class="single-item-section mt-4" /> -->
    <!-- <MenuSection v-else title="充电力值" :items="MenuiIemList.bussiness.recharge" class="single-item-section mt-4" /> -->

    <MenuSection
      v-if="accountRole?.is_admin || accountRole?.is_owner" title="商家中心"
      :items="MenuiIemList.bussiness.businessCenter"
    />
    <MenuSection v-else title="快捷入口" :items="MenuiIemList.client.clientCenter" @bind-phone="bindPhonePoup" @change-check-type="changeCheckType" />

    <MenuSection
      v-if="accountRole?.is_admin || accountRole?.is_owner" title="帮助中心"
      :items="MenuiIemList.bussiness.help"
      @bind-wechat="bindWechatPopup"
    />
    <MenuSection v-else title="帮助中心" :items="MenuiIemList.client.help" @bind-wechat="bindWechatPopup" />
    <view class="menu-item mx-4 mb-2 flex items-center rounded-lg  bg-white  py-3" @click="logout">
      <uni-icons type="undo-filled" size="20" color="#000000" class="mx-3" />
      <button class="item-label flex-1  bg-white text-left  text-base text-gray-800 after:border-none">
        退出登录
      </button>
    </view>
    <Support :version="version" />

    <!-- uni-popup组件 -->
    <uni-popup
      ref="spacePopup" type="bottom" :safe-area="false" background-color="#ffffff"
      borderRadius="20px 20px 0px 0px" @clickaway="closeSpacePopup"
    >
      <view class="popup-content flex">
        <view class="flex w-full flex-col items-center justify-center">
          <view
            v-for="(option, index) in spaceOptions" :key="index"
            class="space-option cursor-pointer border-b border-gray-200 p-2" @click="handleSpaceSelection(option)"
          >
            <view class="space-option-name text-center text-lg font-medium">
              {{ option.company_name }}
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>

  <!-- 手机号绑定弹窗组件 -->
  <PhoneBindPopup
    :visible="showPhoneBindPopup"
    :check-type="checkType"
    @on-success-callback="handlePhoneBindSuccess"
  />

  <!-- 微信绑定弹窗组件 -->
  <WechatBindPopup
    v-model:visible="showWechatBindPopup"
    :on-success-callback="handleWechatBindSuccess"
    @success="handleWechatBindSuccess"
  />
</template>

<style scoped>
.user-avatar {
  @apply mr-1 size-12 rounded-full;
}

.user-details {
  @apply flex flex-col;
}

.user-name {
  @apply text-base font-medium text-gray-900 w-28 truncate;
}

.business-info {
  @apply text-xs text-blue-500 mt-1;
}

.single-item-section {
  @apply mt-4;
}

.space-switcher {
  @apply bg-blue-500 text-white px-1 w-32 py-1 rounded-full cursor-pointer transition-all duration-300 shadow-md flex items-center justify-center;
}

.space-switcher:hover {
  @apply bg-blue-600 shadow-lg transform scale-105;
}

.space-text {
  @apply mr-1;
}

.space-option {
  @apply w-full;
}

.space-option-name {
  @apply text-lg font-medium;
}

.space-option-description {
  @apply text-sm text-gray-500;
}

.space-current {
  @apply text-sm font-medium;
}

/* 菜单项样式 */
.menu-item {
  @apply mx-4 mb-2 flex items-center rounded-lg bg-white py-3;
}

.item-label {
  @apply flex-1 bg-white text-left text-base text-gray-800 after:border-none;
}
</style>
